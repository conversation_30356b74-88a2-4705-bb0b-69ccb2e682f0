version: '3.9'

services:
  mcp-server:
    build:
      context: ./apps/mcp-server
    container_name: mcp-server
    ports:
      - "4000:4000"
    env_file:
      - .env.local
    environment:
      - CLIENT_ID=${CLIENT_ID:-acme}
    depends_on:
      - n8n
      - langchain-agent
      - milvus
    networks:
      - g3-internal

  langchain-agent:
    build:
      context: ./apps/langchain-agent
    container_name: langchain-agent
    ports:
      - "3001:3001"
    env_file:
      - .env.local
    networks:
      - g3-internal
    depends_on:
      - milvus

  n8n:
    image: n8nio/n8n:latest
    container_name: n8n
    ports:
      - "5678:5678"
    environment:
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      # Database configuration
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=db
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=dapth
      - DB_POSTGRESDB_USER=dapth
      - DB_POSTGRESDB_PASSWORD=password
      - DB_POSTGRESDB_SCHEMA=n8n
      # Basic auth (optional - remove these lines to disable)
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=n8n_password
      # Timezone
      - GENERIC_TIMEZONE=UTC
      # Execution data settings
      - EXECUTIONS_PROCESS=main
      - N8N_METRICS=false
    volumes:
      - n8n-data:/home/<USER>/.n8n
    depends_on:
      - db
    networks:
      - g3-internal
    restart: unless-stopped

  db:
    container_name: postgres-db
    image: postgres:13
    environment:
      POSTGRES_USER: dapth
      POSTGRES_PASSWORD: password
      POSTGRES_DB: dapth
    ports:
      - "5434:5432"
    volumes:
      # Initialize n8n schema
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - g3-internal

  pgadmin:
    container_name: pgadmin
    image: dpage/pgadmin4
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: password
      PGADMIN_CONFIG_SERVER_MODE: "False"
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: "False"
      PGADMIN_LISTEN_PORT: 5050
    volumes:
      - ./pgadmin/servers.json:/pgladmin4/servers.json
      - ./pgadmin/pgpass:/pgpass
    ports:
      - "5050:5050"
    networks:
      - g3-internal
  milvus:
    image: milvusdb/milvus:v2.4.0
    container_name: milvus
    ports:
      - "19530:19530"
    environment:
      - MILVUS_LOG_LEVEL=debug
    networks:
      - g3-internal

  langsmith:
    image: langchain/langsmith:latest
    container_name: langsmith
    ports:
      - "8000:8000"
    env_file:
      - .env.local
    networks:
      - g3-internal

networks:
  g3-internal:
    driver: bridge

volumes:
  n8n_data: